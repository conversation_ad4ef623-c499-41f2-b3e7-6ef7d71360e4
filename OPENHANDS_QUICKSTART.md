# OpenHands Quick Start Guide

This guide will help you quickly set up and run All-Hands AI OpenHands with comprehensive configuration including MCP servers and multiple LLM models.

## 🚀 Quick Setup

### 1. Prerequisites

- Docker and Docker Compose installed
- Python 3.8+ (for MCP server setup)
- API keys for your preferred LLM providers

### 2. Configure API Keys

Edit the `.env.ai` file and uncomment/set your API keys:

```bash
# For OpenAI models (GPT-4, GPT-3.5-turbo)
LLM_API_KEY=your-openai-api-key-here

# For Anthropic models (Claude)
ANTHROPIC_API_KEY=your-anthropic-api-key-here
```

### 3. Install MCP Servers (Optional but Recommended)

Run the setup script to install MCP servers that extend OpenHands capabilities:

```bash
# Install uvx first if you don't have it
pip install uvx

# Run the MCP server setup script
./config/setup-mcp-servers.sh
```

### 4. Validate Configuration

Check that your configuration is valid:

```bash
cd config && python3 validate-config.py
```

### 5. Start OpenHands

```bash
docker-compose -f docker-compose.ai.all-hands.yaml up -d
```

### 6. Access the Interface

Open your browser and go to: `http://localhost:3333`

## 🎯 What You Get

### Multiple LLM Configurations

- **Default (GPT-4)**: High-quality responses for complex tasks
- **Cost Optimized (GPT-3.5-turbo)**: Cheaper option for routine tasks
- **Creative (GPT-4)**: High temperature for creative tasks
- **Local (Ollama)**: Local model for privacy/offline use
- **Claude**: Anthropic's Claude 3.5 Sonnet
- **Draft Editor**: Specialized for code editing

### MCP Server Capabilities

- **Fetch Server**: Web scraping and HTTP requests
- **Filesystem Server**: Enhanced file operations
- **Git Server**: Version control operations

### Agent Configurations

- **RepoExplorerAgent**: Uses cost-optimized model for repository exploration
- **CodeWriterAgent**: Uses creative model for code generation
- **CodeActAgent**: Uses default model for general tasks

## 🔧 Configuration Files

- `config/openhands.toml` - Main configuration file
- `.env.ai` - Environment variables and API keys
- `docker-compose.ai.all-hands.yaml` - Docker compose setup

## 📋 Available Models

### OpenAI Models
- `gpt-4` (default)
- `gpt-3.5-turbo` (cost-optimized)

### Anthropic Models
- `claude-3-5-sonnet-20241022`

### Local Models (via Ollama)
- `eramax/openhands-lm-32b-v0.1`

## 🛠 Customization

### Adding New LLM Configurations

Edit `config/openhands.toml` and add a new section:

```toml
[llm.my_custom_model]
model = "your-model-name"
api_key = ""  # Set via environment variable
temperature = 0.5
max_output_tokens = 1500
```

### Using Custom Configurations

Assign custom configurations to specific agents:

```toml
[agent.MyCustomAgent]
llm_config = "my_custom_model"
enable_browsing = true
```

### Adding MCP Servers

Add new stdio servers to the configuration:

```toml
stdio_servers = [
    # ... existing servers ...
    {
        name="my-custom-tool",
        command="python",
        args=["-m", "my_custom_mcp_server"],
        env={"CONFIG_PATH": "/path/to/config"}
    }
]
```

## 🔍 Monitoring and Debugging

### Check Logs

```bash
# OpenHands logs
docker-compose -f docker-compose.ai.all-hands.yaml logs openhands

# Ollama logs
docker-compose -f docker-compose.ai.all-hands.yaml logs ollama
```

### Enable Debug Mode

Edit `config/openhands.toml`:

```toml
[core]
debug = true
```

### Validate Configuration

```bash
cd config && python3 validate-config.py
```

## 🔒 Security Notes

1. **Never commit API keys** to version control
2. **Change the JWT secret** in production
3. **Review file upload restrictions** for your use case
4. **Enable confirmation mode** for production environments

## 📚 Additional Resources

- [OpenHands Documentation](https://docs.all-hands.dev/)
- [Model Context Protocol](https://modelcontextprotocol.io/)
- [Configuration Guide](config/README.md)

## 🆘 Troubleshooting

### Common Issues

1. **Port conflicts**: Change `OPENHANDS_PORT` in `.env.ai`
2. **API key errors**: Verify keys are set correctly in `.env.ai`
3. **MCP servers not working**: Run `./config/setup-mcp-servers.sh`
4. **Configuration errors**: Run `cd config && python3 validate-config.py`

### Getting Help

- Check the logs for error messages
- Validate your configuration
- Review the documentation links above
- Check GitHub issues for similar problems

## 🎉 You're Ready!

Your OpenHands setup now includes:
- ✅ Multiple LLM model configurations
- ✅ MCP servers for extended capabilities
- ✅ Agent-specific configurations
- ✅ Comprehensive security settings
- ✅ Validation and debugging tools

Happy coding with OpenHands! 🚀
