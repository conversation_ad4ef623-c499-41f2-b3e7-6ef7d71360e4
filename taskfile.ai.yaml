version: '3'

vars:
  DOCKER_COMPOSE: 'docker compose -f docker-compose.ai.all-hands.yaml'
  CONFIG_DIR: './config'
  ENV_FILE: '.env.ai'

dotenv: ['.env.ai']

tasks:
  # =============================================================================
  # SETUP AND INITIALIZATION
  # =============================================================================

  setup:
    desc: 'Complete setup for OpenHands with configuration and MCP servers'
    cmds:
      - task: init
      - task: config:validate
      - task: mcp:setup
      - echo "✅ OpenHands setup complete! Run 'task start' to begin."

  init:
    desc: 'Initialize OpenHands environment and configuration'
    cmds:
      - echo "🚀 Initializing OpenHands environment..."
      - mkdir -p {{.CONFIG_DIR}}
      - cp -n .env.ai.example {{.ENV_FILE}} || true
      - echo "📝 Environment file ready at {{.ENV_FILE}}"
      - echo "⚠️  Please configure your API keys in {{.ENV_FILE}}"

  # =============================================================================
  # CONFIGURATION MANAGEMENT
  # =============================================================================

  config:validate:
    desc: 'Validate OpenHands configuration'
    dir: '{{.CONFIG_DIR}}'
    cmds:
      - echo "🔍 Validating OpenHands configuration..."
      - python3 validate-config.py

  config:show:
    desc: 'Show current configuration summary'
    cmds:
      - echo "📋 OpenHands Configuration Summary:"
      - echo "   Config file: {{.CONFIG_DIR}}/openhands.toml"
      - echo "   Environment: {{.ENV_FILE}}"
      - echo "   Docker compose: docker-compose.ai.all-hands.yaml"
      - |
        if [ -f "{{.ENV_FILE}}" ]; then
          echo "   OpenHands port: ${OPENHANDS_PORT:-3333}"
          echo "   Ollama port: ${OLLAMA_PORT:-11434}"
        fi

  # =============================================================================
  # MCP SERVER MANAGEMENT
  # =============================================================================

  mcp:setup:
    desc: 'Install and setup MCP servers'
    cmds:
      - echo "📦 Setting up MCP servers..."
      - chmod +x {{.CONFIG_DIR}}/setup-mcp-servers.sh
      - '{{.CONFIG_DIR}}/setup-mcp-servers.sh'

  mcp:list:
    desc: 'List installed MCP servers'
    cmds:
      - echo "📋 Installed MCP servers:"
      - uvx list | grep mcp-server || echo "No MCP servers found"

  mcp:test:
    desc: 'Test MCP server installations'
    cmds:
      - echo "🧪 Testing MCP servers..."
      - uvx run mcp-server-fetch --help || echo "❌ mcp-server-fetch not working"
      - uvx run mcp-server-filesystem --help || echo "❌ mcp-server-filesystem not working"
      - uvx run mcp-server-git --help || echo "❌ mcp-server-git not working"

  # =============================================================================
  # DOCKER COMPOSE OPERATIONS
  # =============================================================================

  run:
    desc: 'Run a docker compose command'
    cmds:
      - '{{.DOCKER_COMPOSE}} {{.CLI_ARGS}}'

  # =============================================================================
  # APPLICATION LIFECYCLE
  # =============================================================================

  start:
    desc: 'Start OpenHands with full configuration'
    deps: [config:validate]
    cmds:
      - echo "🚀 Starting OpenHands..."
      - '{{.DOCKER_COMPOSE}} up -d --remove-orphans'
      - task: status
      - echo "🎉 OpenHands is running!"
      - echo "   Web interface: http://localhost:${OPENHANDS_PORT:-3333}"
      - echo "   Ollama API: http://localhost:${OLLAMA_PORT:-11434}"

  start:openhands:
    desc: 'Start only OpenHands (without Ollama)'
    deps: [config:validate]
    cmds:
      - echo "🚀 Starting OpenHands only..."
      - '{{.DOCKER_COMPOSE}} up openhands -d --remove-orphans'
      - task: status:openhands
      - echo "🎉 OpenHands is running at http://localhost:${OPENHANDS_PORT:-3333}"

  start:ollama:
    desc: 'Start only Ollama server'
    cmds:
      - echo "🚀 Starting Ollama server..."
      - '{{.DOCKER_COMPOSE}} up ollama -d'
      - task: status:ollama
      - echo "🎉 Ollama is running at http://localhost:${OLLAMA_PORT:-11434}"

  stop:
    desc: 'Stop all OpenHands services'
    cmds:
      - echo "🛑 Stopping OpenHands services..."
      - '{{.DOCKER_COMPOSE}} down'
      - echo "✅ Services stopped"

  restart:
    desc: 'Restart OpenHands services'
    cmds:
      - task: stop
      - task: start

  # =============================================================================
  # MONITORING AND DEBUGGING
  # =============================================================================

  status:
    desc: 'Show status of all services'
    cmds:
      - echo "📊 Service Status:"
      - '{{.DOCKER_COMPOSE}} ps'

  status:openhands:
    desc: 'Show OpenHands service status'
    cmds:
      - echo "📊 OpenHands Status:"
      - '{{.DOCKER_COMPOSE}} ps openhands'

  status:ollama:
    desc: 'Show Ollama service status'
    cmds:
      - echo "📊 Ollama Status:"
      - '{{.DOCKER_COMPOSE}} ps ollama'

  logs:
    desc: 'Show logs from all services'
    cmds:
      - '{{.DOCKER_COMPOSE}} logs -f {{.CLI_ARGS}}'

  logs:openhands:
    desc: 'Show OpenHands logs'
    cmds:
      - '{{.DOCKER_COMPOSE}} logs -f openhands'

  logs:ollama:
    desc: 'Show Ollama logs'
    cmds:
      - '{{.DOCKER_COMPOSE}} logs -f ollama'

  # =============================================================================
  # SHELL ACCESS
  # =============================================================================

  shell:
    desc: 'Open shell in OpenHands container'
    cmds:
      - '{{.DOCKER_COMPOSE}} exec openhands sh'

  shell:ollama:
    desc: 'Open shell in Ollama container'
    cmds:
      - '{{.DOCKER_COMPOSE}} exec ollama sh'
