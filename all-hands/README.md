# OpenHands Configuration Guide

This directory contains configuration files for All-Hands AI OpenHands, including comprehensive settings for LLM models, MCP servers, agents, and sandbox environments.

## Files

- `openhands.toml` - Main configuration file for OpenHands
- `README.md` - This documentation file

## Configuration Overview

The `openhands.toml` file is structured into several sections:

### 1. Core Configuration (`[core]`)
- Basic workspace and file management settings
- Task management and iteration limits
- Debugging and logging options
- Trajectory recording for session replay

### 2. LLM Configuration

#### Default LLM (`[llm]`)
- Primary model configuration (GPT-4 by default)
- API settings, retry logic, and cost tracking

#### Custom LLM Configurations
- `[llm.cost_optimized]` - GPT-3.5-turbo for cost-effective operations
- `[llm.creative]` - High-temperature GPT-4 for creative tasks
- `[llm.local]` - Local Ollama model configuration
- `[llm.draft_editor]` - Specialized model for code editing
- `[llm.claude]` - Claude 3.5 Sonnet configuration

### 3. MCP (Model Context Protocol) (`[mcp]`)
Extends OpenHands capabilities with external tools:

#### SSE Servers
- External servers communicating via Server-Sent Events
- Support for API key authentication

#### Stdio Servers
- Local processes for enhanced functionality:
  - `fetch` - Web scraping capabilities
  - `filesystem` - Enhanced file operations
  - `git` - Version control operations

### 4. Agent Configuration (`[agent]`)
- Function calling and browsing capabilities
- LLM editor and Jupyter integration
- Microagent settings
- Agent-specific configurations for different use cases

### 5. Sandbox Configuration (`[sandbox]`)
- Container image and runtime settings
- Networking and security options
- Development tools and plugins
- Environment variables and dependencies

### 6. Security Configuration (`[security]`)
- Confirmation mode for dangerous operations
- Security analyzer integration

## Setup Instructions

### 1. Environment Variables

Create or update your `.env.ai` file with the following variables:

```bash
# OpenHands Configuration
OPENHANDS_PORT=3333
OLLAMA_PORT=11434
LOG_ALL_EVENTS=true

# LLM API Keys (set these according to your providers)
LLM_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Custom runtime image (if using custom sandbox)
# SANDBOX_RUNTIME_CONTAINER_IMAGE=salesto:sandbox
```

### 2. MCP Server Setup

To use the configured MCP servers, you'll need to install them:

```bash
# Install MCP servers using uvx
uvx install mcp-server-fetch
uvx install mcp-server-filesystem
uvx install mcp-server-git
```

### 3. Docker Compose

The configuration is automatically mounted in the docker-compose setup:

```bash
# Start OpenHands with configuration
docker-compose -f docker-compose.ai.all-hands.yaml up -d
```

## Customization

### Adding New LLM Configurations

To add a new LLM configuration, create a new section in the TOML file:

```toml
[llm.my_custom_model]
model = "your-model-name"
api_key = "your-api-key"
temperature = 0.5
max_output_tokens = 1500
```

### Adding MCP Servers

#### SSE Server Example:
```toml
sse_servers = [
    {url="https://your-server.com/mcp", api_key="your-api-key"}
]
```

#### Stdio Server Example:
```toml
stdio_servers = [
    {
        name="custom-tool",
        command="python",
        args=["-m", "your_custom_mcp_server"],
        env={"CONFIG_PATH": "/path/to/config"}
    }
]
```

### Agent-Specific Configurations

Configure specific agents to use different LLM configurations:

```toml
[agent.YourCustomAgent]
llm_config = "cost_optimized"
enable_browsing = true
enable_llm_editor = true
```

## Security Considerations

1. **API Keys**: Never commit API keys to version control. Use environment variables.
2. **JWT Secret**: Change the default JWT secret in production.
3. **File Permissions**: The configuration file is mounted read-only in the container.
4. **Confirmation Mode**: Enable for production environments with sensitive operations.

## Troubleshooting

### Common Issues

1. **Configuration Not Loading**: Ensure the file is properly mounted in docker-compose
2. **MCP Servers Not Working**: Check that servers are installed and accessible
3. **API Key Issues**: Verify environment variables are set correctly
4. **Model Access**: Ensure you have access to the configured models

### Debugging

Enable debug mode in the configuration:

```toml
[core]
debug = true
```

### Logs

Check OpenHands logs for configuration issues:

```bash
docker-compose -f docker-compose.ai.all-hands.yaml logs openhands
```

## Best Practices

1. **Cost Management**: Use cost-optimized models for routine tasks
2. **Model Selection**: Match model capabilities to task requirements
3. **MCP Integration**: Start with basic MCP servers and add complexity gradually
4. **Security**: Regularly review and update security settings
5. **Monitoring**: Track usage and costs through the configured cost tracking

## References

- [OpenHands Documentation](https://docs.all-hands.dev/)
- [Model Context Protocol](https://modelcontextprotocol.io/)
- [Custom LLM Configurations](https://docs.all-hands.dev/modules/usage/llms/custom-llm-configs)
- [MCP Configuration](https://docs.all-hands.dev/modules/usage/mcp)
