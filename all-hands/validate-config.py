#!/usr/bin/env python3
"""
Configuration validation script for OpenHands
Validates the TOML configuration file for syntax and common issues
"""

import sys
import os
from pathlib import Path

try:
    import tomllib
except ImportError:
    try:
        import tomli as tomllib
    except ImportError:
        print("❌ Error: tomllib/tomli not available. Install with: pip install tomli")
        sys.exit(1)

def validate_config(config_path: str) -> bool:
    """Validate the OpenHands configuration file"""
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        return False
    
    try:
        with open(config_path, 'rb') as f:
            config = tomllib.load(f)
    except Exception as e:
        print(f"❌ Invalid TOML syntax: {e}")
        return False
    
    print("✅ TOML syntax is valid")
    
    # Validate required sections
    required_sections = ['core', 'llm', 'agent', 'sandbox']
    missing_sections = []
    
    for section in required_sections:
        if section not in config:
            missing_sections.append(section)
    
    if missing_sections:
        print(f"⚠️  Missing required sections: {', '.join(missing_sections)}")
    else:
        print("✅ All required sections present")
    
    # Validate LLM configurations
    llm_configs = [key for key in config.keys() if key.startswith('llm.')]
    if llm_configs:
        print(f"✅ Found {len(llm_configs)} custom LLM configurations: {', '.join([k.replace('llm.', '') for k in llm_configs])}")
    
    # Validate MCP configuration
    if 'mcp' in config:
        mcp_config = config['mcp']
        sse_servers = mcp_config.get('sse_servers', [])
        stdio_servers = mcp_config.get('stdio_servers', [])
        
        print(f"✅ MCP configuration found:")
        print(f"   - SSE servers: {len(sse_servers)}")
        print(f"   - Stdio servers: {len(stdio_servers)}")
        
        # Validate stdio servers
        for i, server in enumerate(stdio_servers):
            if 'name' not in server or 'command' not in server:
                print(f"⚠️  Stdio server {i} missing required 'name' or 'command'")
    else:
        print("⚠️  No MCP configuration found")
    
    # Check for common configuration issues
    warnings = []
    
    # Check JWT secret
    if config.get('core', {}).get('jwt_secret') == 'your-secure-jwt-secret-here':
        warnings.append("JWT secret is using default value - change this in production")
    
    # Check for empty API keys in LLM configs
    for section_name, section_config in config.items():
        if section_name.startswith('llm') and isinstance(section_config, dict):
            if section_config.get('api_key') == '':
                warnings.append(f"API key is empty in {section_name} - set via environment variable")
    
    if warnings:
        print("\n⚠️  Warnings:")
        for warning in warnings:
            print(f"   - {warning}")
    
    print("\n🎉 Configuration validation complete!")
    return True

def main():
    """Main function"""
    config_path = os.path.join(os.path.dirname(__file__), 'openhands.toml')
    
    print("🔍 Validating OpenHands configuration...")
    print(f"📁 Config file: {config_path}")
    print()
    
    if validate_config(config_path):
        print("\n✅ Configuration is valid!")
        sys.exit(0)
    else:
        print("\n❌ Configuration validation failed!")
        sys.exit(1)

if __name__ == '__main__':
    main()
