#!/bin/bash

# Setup script for MCP servers used in OpenHands configuration
# This script installs the MCP servers configured in openhands.toml

set -e

echo "🚀 Setting up MCP servers for OpenHands..."

# Check if uvx is installed
if ! command -v uvx &> /dev/null; then
    echo "❌ uvx is not installed. Please install it first:"
    echo "   pip install uvx"
    exit 1
fi

echo "✅ uvx found"

# Install MCP servers
echo "📦 Installing MCP servers..."

echo "  - Installing mcp-server-fetch..."
uvx install mcp-server-fetch

echo "  - Installing mcp-server-filesystem..."
uvx install mcp-server-filesystem

echo "  - Installing mcp-server-git..."
uvx install mcp-server-git

echo "✅ MCP servers installed successfully!"

# Verify installations
echo "🔍 Verifying installations..."

if uvx list | grep -q "mcp-server-fetch"; then
    echo "  ✅ mcp-server-fetch installed"
else
    echo "  ❌ mcp-server-fetch installation failed"
fi

if uvx list | grep -q "mcp-server-filesystem"; then
    echo "  ✅ mcp-server-filesystem installed"
else
    echo "  ❌ mcp-server-filesystem installation failed"
fi

if uvx list | grep -q "mcp-server-git"; then
    echo "  ✅ mcp-server-git installed"
else
    echo "  ❌ mcp-server-git installation failed"
fi

echo ""
echo "🎉 MCP server setup complete!"
echo ""
echo "Next steps:"
echo "1. Configure your API keys in .env.ai"
echo "2. Start OpenHands with: docker-compose -f docker-compose.ai.all-hands.yaml up -d"
echo "3. Check the configuration in the OpenHands UI under Settings > MCP"
echo ""
echo "For more information, see config/README.md"
