# OpenHands Configuration File
# This file contains comprehensive configuration for All-Hands AI OpenHands
# including LLM models, MCP servers, agents, and sandbox settings

# =============================================================================
# CORE CONFIGURATION
# =============================================================================
[core]
# Workspace and file management
cache_dir = "/tmp/cache"
file_store_path = "/tmp/file_store"
file_store = "memory"

# File upload restrictions
file_uploads_allowed_extensions = [".*"]
file_uploads_max_file_size_mb = 100
file_uploads_restrict_file_types = false

# Task management
max_budget_per_task = 0.0  # 0.0 means no limit
max_iterations = 100

# Debugging and logging
debug = false
disable_color = false

# Trajectories for session recording/replay
save_trajectory_path = "./trajectories"
replay_trajectory_path = ""

# Runtime and agent defaults
runtime = "docker"
default_agent = "CodeActAgent"
run_as_openhands = true

# JWT secret for authentication (change this in production)
jwt_secret = "your-secure-jwt-secret-here"

# =============================================================================
# LLM CONFIGURATION - DEFAULT
# =============================================================================
[llm]
# Default model configuration (GPT-4)
model = "gpt-4"
api_key = ""  # Set via environment variable LLM_API_KEY
base_url = ""
api_version = ""

# Model parameters
temperature = 0.0
top_p = 1.0
max_input_tokens = 0
max_output_tokens = 0

# Message handling
max_message_chars = 30000

# Retry configuration
num_retries = 8
retry_min_wait = 15
retry_max_wait = 120
retry_multiplier = 2.0

# Advanced options
drop_params = false
caching_prompt = true
timeout = 0
disable_vision = false

# Cost tracking
input_cost_per_token = 0.0
output_cost_per_token = 0.0

# =============================================================================
# CUSTOM LLM CONFIGURATIONS
# =============================================================================

# Cost-optimized configuration using GPT-3.5-turbo
[llm.cost_optimized]
model = "gpt-3.5-turbo"
api_key = ""  # Inherits from default if not set
temperature = 0.2
max_output_tokens = 1000

# High creativity configuration for creative tasks
[llm.creative]
model = "gpt-4"
temperature = 0.8
top_p = 0.9
max_output_tokens = 2000

# Local Ollama configuration
[llm.local]
model = "eramax/openhands-lm-32b-v0.1"
base_url = "http://ollama:11434"
custom_llm_provider = "ollama"
temperature = 0.1

# Draft editor configuration for code editing
[llm.draft_editor]
model = "gpt-4"
temperature = 0.2
top_p = 0.95
presence_penalty = 0.0
frequency_penalty = 0.0

# Claude configuration
[llm.claude]
model = "claude-3-5-sonnet-20241022"
api_key = ""  # Set via environment variable
temperature = 0.0

# =============================================================================
# MCP (Model Context Protocol) CONFIGURATION
# =============================================================================
[mcp]
# SSE Servers - External servers that communicate via Server-Sent Events
sse_servers = [
    # Example SSE server with basic URL
    # "http://example.com:8080/mcp",

    # Example SSE server with API key authentication
    # {url="https://secure-example.com/mcp", api_key="your-api-key"}
]

# Stdio Servers - Local processes that communicate via standard input/output
stdio_servers = [
    # Fetch server for web scraping capabilities
    {name="fetch", command="uvx", args=["mcp-server-fetch"]},

    # File system server for enhanced file operations
    {name="filesystem", command="uvx", args=["mcp-server-filesystem"], env={"ALLOWED_DIRS"="/workspace"}},

    # Git server for version control operations
    {name="git", command="uvx", args=["mcp-server-git"]},

    # Example custom data processor
    # {
    #     name="data-processor",
    #     command="python",
    #     args=["-m", "my_mcp_server"],
    #     env={
    #         "DEBUG": "true",
    #         "PORT": "8080"
    #     }
    # }
]

# =============================================================================
# AGENT CONFIGURATION
# =============================================================================
[agent]
# Default agent settings
function_calling = true
enable_browsing = false
enable_llm_editor = true
enable_jupyter = false
enable_history_truncation = true

# Microagent settings
enable_prompt_extensions = true
disabled_microagents = []

# Specific agent configurations
[agent.RepoExplorerAgent]
llm_config = "cost_optimized"

[agent.CodeWriterAgent]
llm_config = "creative"
enable_llm_editor = true

[agent.CodeActAgent]
llm_config = "default"
function_calling = true

# =============================================================================
# SANDBOX CONFIGURATION
# =============================================================================
[sandbox]
# Execution settings
timeout = 120
user_id = 1000

# Container configuration
base_container_image = "nikolaik/python-nodejs:python3.12-nodejs22"

# Networking
use_host_network = false
runtime_binding_address = "0.0.0.0"

# Development and plugins
enable_auto_lint = true
initialize_plugins = true

# Runtime environment
runtime_extra_deps = ""
runtime_startup_env_vars = {}

# Evaluation settings
browsergym_eval_env = ""

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
[security]
# Enable confirmation mode for potentially dangerous operations
confirmation_mode = false

# Security analyzer (if available)
security_analyzer = ""
